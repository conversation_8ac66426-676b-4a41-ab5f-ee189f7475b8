import { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { AlertCircle, Mail, Shield, CheckCircle } from "lucide-react";
import vmakeLogo from "@assets/339076826_1147709369229224_1319750110613322317_n.jpg";

interface TokenVerificationProps {
  token: string;
  onSuccess: () => void;
}

export default function TokenVerification({ token, onSuccess }: TokenVerificationProps) {
  const [step, setStep] = useState<'validating' | 'otp' | 'success' | 'error'>('validating');
  const [otpCode, setOtpCode] = useState("");
  const [email, setEmail] = useState("");
  const [tokenId, setTokenId] = useState<number | null>(null);
  const [attemptsRemaining, setAttemptsRemaining] = useState(3);
  const [errorMessage, setErrorMessage] = useState("");
  const { toast } = useToast();

  // Validate token on component mount
  const validateTokenMutation = useMutation({
    mutationFn: async (token: string) => {
      const response = await apiRequest("GET", `/api/auth/validate-token?token=${token}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Token validation failed');
      }
      return response.json();
    },
    onSuccess: (data) => {
      if (data.requiresOTP) {
        setStep('otp');
        setEmail(data.email);
        setTokenId(data.tokenId);
        toast({
          title: "Verification Required",
          description: "Please check your email for the verification code.",
        });
      } else {
        setStep('success');
        setTimeout(() => {
          onSuccess();
        }, 2000);
      }
    },
    onError: (error: any) => {
      setStep('error');
      setErrorMessage(error.message);
      toast({
        title: "Access Denied",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Verify OTP
  const verifyOTPMutation = useMutation({
    mutationFn: async (data: { email: string; tokenId: number; otpCode: string }) => {
      const response = await apiRequest("POST", "/api/auth/verify-otp", data);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'OTP verification failed');
      }
      return response.json();
    },
    onSuccess: (data) => {
      setStep('success');
      queryClient.invalidateQueries({ queryKey: ["/api/auth/me"] });
      toast({
        title: "Access Granted",
        description: "Welcome to VMake Catalog!",
      });
      setTimeout(() => {
        onSuccess();
      }, 2000);
    },
    onError: (error: any) => {
      if (error.message.includes('Too many failed attempts')) {
        setStep('error');
        setErrorMessage(error.message);
      } else {
        setAttemptsRemaining(prev => prev - 1);
        toast({
          title: "Invalid Code",
          description: error.message,
          variant: "destructive",
        });
      }
    },
  });

  useEffect(() => {
    if (token) {
      validateTokenMutation.mutate(token);
    }
  }, [token]);

  const handleOTPSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!otpCode.trim() || !email || !tokenId) {
      toast({
        title: "Invalid Input",
        description: "Please enter the verification code.",
        variant: "destructive",
      });
      return;
    }

    verifyOTPMutation.mutate({
      email,
      tokenId,
      otpCode: otpCode.trim()
    });
  };

  const renderContent = () => {
    switch (step) {
      case 'validating':
        return (
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-white mb-2">Validating Access</h2>
            <p className="text-gray-400">Please wait while we verify your access link...</p>
          </div>
        );

      case 'otp':
        return (
          <div>
            <div className="flex items-center justify-center mb-6">
              <div className="bg-gold/20 p-3 rounded-full">
                <Mail className="w-8 h-8 text-gold" />
              </div>
            </div>
            
            <h2 className="text-xl font-semibold text-white text-center mb-2">
              Email Verification
            </h2>
            <p className="text-gray-400 text-center mb-6">
              We've sent a 6-digit verification code to<br />
              <span className="text-gold font-medium">{email}</span>
            </p>

            <form onSubmit={handleOTPSubmit} className="space-y-4">
              <div>
                <Label className="block text-sm font-medium text-gray-300 mb-2">
                  Verification Code
                </Label>
                <Input
                  type="text"
                  placeholder="Enter 6-digit code"
                  value={otpCode}
                  onChange={(e) => setOtpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  className="text-center text-lg tracking-widest bg-black-primary border-black-accent text-white placeholder-gray-500"
                  disabled={verifyOTPMutation.isPending}
                  maxLength={6}
                />
              </div>

              {attemptsRemaining < 3 && (
                <div className="flex items-center gap-2 text-yellow-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  <span>{attemptsRemaining} attempts remaining</span>
                </div>
              )}

              <Button
                type="submit"
                disabled={verifyOTPMutation.isPending || otpCode.length !== 6}
                className="w-full bg-gold hover:bg-gold-light text-black-primary font-semibold"
              >
                {verifyOTPMutation.isPending ? "Verifying..." : "Verify Code"}
              </Button>
            </form>

            <p className="text-gray-500 text-xs text-center mt-4">
              Didn't receive the code? Check your spam folder or contact support.
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-green-500/20 p-3 rounded-full">
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </div>
            
            <h2 className="text-xl font-semibold text-white mb-2">Access Granted!</h2>
            <p className="text-gray-400 mb-4">
              Welcome to VMake Catalog. Redirecting you now...
            </p>
            <div className="animate-pulse text-gold">●●●</div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-red-500/20 p-3 rounded-full">
                <AlertCircle className="w-8 h-8 text-red-500" />
              </div>
            </div>
            
            <h2 className="text-xl font-semibold text-white mb-2">Access Denied</h2>
            <p className="text-gray-400 mb-4">{errorMessage}</p>
            <p className="text-gray-500 text-sm">
              Please contact support if you believe this is an error.
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-black-primary flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-black-secondary border-black-accent">
        <CardContent className="pt-6">
          {/* Logo */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gold rounded flex items-center justify-center overflow-hidden">
                <img
                  src={vmakeLogo}
                  alt="Vmake Finessee Logo"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h1 className="font-bold text-lg text-white">Vmake</h1>
                <p className="text-xs text-gold">Finessee</p>
              </div>
            </div>
          </div>

          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
}
